const { test, expect } = require("@playwright/test");

test("Product Login API Test", async ({ request }) => {
  const response = await request.post("/chat/api/auth/login", {
    data: {
      username: "admin",
      password: "admin123",
    },
  });

  expect(response.status()).toBe(200); // Status code check
  const body = await response.json();
  expect(body).toHaveProperty("token"); // Token return aagudhu check panna
});
